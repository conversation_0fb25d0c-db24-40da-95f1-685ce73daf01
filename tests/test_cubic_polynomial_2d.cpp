#include <gtest/gtest.h>
#include "cubic_polynomial_2d.hpp"
#include <cmath>

using namespace curves;

class CubicPolynomial2DTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Test polynomial: 
        // x(s) = 2s³ - 3s² + s + 5
        // y(s) = s³ + 2s² - s + 1
        // Coefficients: [a₁, b₁, c₁, d₁, a₂, b₂, c₂, d₂]
        test_coeffs << 2.0, -3.0, 1.0, 5.0, 1.0, 2.0, -1.0, 1.0;
    }

    Coefficients2D test_coeffs;
    const double tolerance = 1e-10;
};

TEST_F(CubicPolynomial2DTest, EvaluateBasic) {
    // Test evaluation at s = 0
    Point2D result0 = Evaluate(test_coeffs, 0.0);
    EXPECT_NEAR(result0.x(), 5.0, tolerance); // x(0) = 5
    EXPECT_NEAR(result0.y(), 1.0, tolerance); // y(0) = 1

    // Test evaluation at s = 1
    Point2D result1 = Evaluate(test_coeffs, 1.0);
    EXPECT_NEAR(result1.x(), 5.0, tolerance); // x(1) = 2 - 3 + 1 + 5 = 5
    EXPECT_NEAR(result1.y(), 3.0, tolerance); // y(1) = 1 + 2 - 1 + 1 = 3

    // Test evaluation at s = 2
    Point2D result2 = Evaluate(test_coeffs, 2.0);
    EXPECT_NEAR(result2.x(), 11.0, tolerance); // x(2) = 16 - 12 + 2 + 5 = 11
    EXPECT_NEAR(result2.y(), 15.0, tolerance); // y(2) = 8 + 8 - 2 + 1 = 15
}

TEST_F(CubicPolynomial2DTest, EvaluateDerivative) {
    // Test derivative at s = 0
    // x'(s) = 6s² - 6s + 1, y'(s) = 3s² + 4s - 1
    Heading2D deriv0 = EvaluateDerivative(test_coeffs, 0.0);
    EXPECT_NEAR(deriv0.x(), 1.0, tolerance); // x'(0) = 1
    EXPECT_NEAR(deriv0.y(), -1.0, tolerance); // y'(0) = -1

    // Test derivative at s = 1
    Heading2D deriv1 = EvaluateDerivative(test_coeffs, 1.0);
    EXPECT_NEAR(deriv1.x(), 1.0, tolerance); // x'(1) = 6 - 6 + 1 = 1
    EXPECT_NEAR(deriv1.y(), 6.0, tolerance); // y'(1) = 3 + 4 - 1 = 6

    // Test derivative at s = 2
    Heading2D deriv2 = EvaluateDerivative(test_coeffs, 2.0);
    EXPECT_NEAR(deriv2.x(), 13.0, tolerance); // x'(2) = 24 - 12 + 1 = 13
    EXPECT_NEAR(deriv2.y(), 19.0, tolerance); // y'(2) = 12 + 8 - 1 = 19
}

TEST_F(CubicPolynomial2DTest, EvaluateSecondDerivative) {
    // Test second derivative at s = 0
    // x''(s) = 12s - 6, y''(s) = 6s + 4
    Heading2D deriv2_0 = EvaluateSecondDerivative(test_coeffs, 0.0);
    EXPECT_NEAR(deriv2_0.x(), -6.0, tolerance); // x''(0) = -6
    EXPECT_NEAR(deriv2_0.y(), 4.0, tolerance); // y''(0) = 4

    // Test second derivative at s = 1
    Heading2D deriv2_1 = EvaluateSecondDerivative(test_coeffs, 1.0);
    EXPECT_NEAR(deriv2_1.x(), 6.0, tolerance); // x''(1) = 12 - 6 = 6
    EXPECT_NEAR(deriv2_1.y(), 10.0, tolerance); // y''(1) = 6 + 4 = 10

    // Test second derivative at s = 0.5
    Heading2D deriv2_half = EvaluateSecondDerivative(test_coeffs, 0.5);
    EXPECT_NEAR(deriv2_half.x(), 0.0, tolerance); // x''(0.5) = 6 - 6 = 0
    EXPECT_NEAR(deriv2_half.y(), 7.0, tolerance); // y''(0.5) = 3 + 4 = 7
}

TEST_F(CubicPolynomial2DTest, FitFourPoints2D) {
    // Create 4 points that should lie on a known polynomial
    std::vector<Point2D> points = {
        Point2D(0.0, 1.0),   // s = 0
        Point2D(1.0, 2.0),   // s = 0.3333
        Point2D(2.0, 3.0),   // s = 0.6666
        Point2D(3.0, 4.0)    // s = 1
    };
    std::vector<double> s_values = {};

    Coefficients2D fitted = FitFourPoints2D(points, s_values);

    // Verify that the fitted polynomial passes through all points
    for (size_t i = 0; i < points.size(); ++i) {
        Point2D evaluated = Evaluate(fitted, s_values[i]);
        EXPECT_NEAR(evaluated.x(), points[i].x(), tolerance);
        EXPECT_NEAR(evaluated.y(), points[i].y(), tolerance);
    }
}

TEST_F(CubicPolynomial2DTest, FitFourPoints2DInvalidInput) {
    std::vector<Point2D> points = {
        Point2D(0.0, 1.0),
        Point2D(1.0, 2.0),
        Point2D(2.0, 3.0)  // Only 3 points
    };
    std::vector<double> s_values = {};

    EXPECT_THROW(FitFourPoints2D(points, s_values), std::invalid_argument);
}

TEST_F(CubicPolynomial2DTest, FitPointHeadingPointHeading2D) {
    // Test point-heading-point-heading fitting
    Point2D point0(0.0, 0.0);
    Heading2D heading0(1.0, 0.0);  // Horizontal heading
    Point2D point1(1.0, 1.0);
    Heading2D heading1(0.0, 1.0);  // Vertical heading

    Coefficients2D fitted = FitPointHeadingPointHeading2D(point0, heading0, point1, heading1);

    // Verify the constraints are satisfied
    Point2D eval_p0 = Evaluate(fitted, 0.0);
    Point2D eval_p1 = Evaluate(fitted, 1.0);
    Heading2D eval_h0 = EvaluateDerivative(fitted, 0.0);
    Heading2D eval_h1 = EvaluateDerivative(fitted, 1.0);

    EXPECT_NEAR(eval_p0.x(), point0.x(), tolerance);
    EXPECT_NEAR(eval_p0.y(), point0.y(), tolerance);
    EXPECT_NEAR(eval_p1.x(), point1.x(), tolerance);
    EXPECT_NEAR(eval_p1.y(), point1.y(), tolerance);
    EXPECT_NEAR(eval_h0.x(), heading0.x(), tolerance);
    EXPECT_NEAR(eval_h0.y(), heading0.y(), tolerance);
    EXPECT_NEAR(eval_h1.x(), heading1.x(), tolerance);
    EXPECT_NEAR(eval_h1.y(), heading1.y(), tolerance);
}

TEST_F(CubicPolynomial2DTest, ToString) {
    Coefficients2D coeffs;
    coeffs << 1.0, -2.0, 3.0, -4.0, 0.5, 1.0, -1.5, 2.0;

    std::string result = ToString(coeffs);

    // Should contain polynomial representations for both x and y
    EXPECT_TRUE(result.find("x(s)") != std::string::npos);
    EXPECT_TRUE(result.find("y(s)") != std::string::npos);
    EXPECT_TRUE(result.find("s³") != std::string::npos);
    EXPECT_TRUE(result.find("s²") != std::string::npos);
    EXPECT_TRUE(result.find("s") != std::string::npos);
}

TEST_F(CubicPolynomial2DTest, EvaluateMultiple) {
    std::vector<double> s_values = {0.0, 1.0, 2.0};
    std::vector<Point2D> results = EvaluateMultiple(test_coeffs, s_values);

    ASSERT_EQ(results.size(), 3);
    
    Point2D expected0 = Evaluate(test_coeffs, 0.0);
    Point2D expected1 = Evaluate(test_coeffs, 1.0);
    Point2D expected2 = Evaluate(test_coeffs, 2.0);

    EXPECT_NEAR(results[0].x(), expected0.x(), tolerance);
    EXPECT_NEAR(results[0].y(), expected0.y(), tolerance);
    EXPECT_NEAR(results[1].x(), expected1.x(), tolerance);
    EXPECT_NEAR(results[1].y(), expected1.y(), tolerance);
    EXPECT_NEAR(results[2].x(), expected2.x(), tolerance);
    EXPECT_NEAR(results[2].y(), expected2.y(), tolerance);
}

TEST_F(CubicPolynomial2DTest, CoefficientsUtilities) {
    Coefficients2D coeffs;
    coeffs << 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0;

    // Test extracting x coefficients
    Eigen::Vector4d x_coeffs = GetXCoeffs(coeffs);
    EXPECT_NEAR(x_coeffs[0], 1.0, tolerance);
    EXPECT_NEAR(x_coeffs[1], 2.0, tolerance);
    EXPECT_NEAR(x_coeffs[2], 3.0, tolerance);
    EXPECT_NEAR(x_coeffs[3], 4.0, tolerance);

    // Test extracting y coefficients
    Eigen::Vector4d y_coeffs = GetYCoeffs(coeffs);
    EXPECT_NEAR(y_coeffs[0], 5.0, tolerance);
    EXPECT_NEAR(y_coeffs[1], 6.0, tolerance);
    EXPECT_NEAR(y_coeffs[2], 7.0, tolerance);
    EXPECT_NEAR(y_coeffs[3], 8.0, tolerance);

    // Test combining coefficients
    Eigen::Vector4d test_x_coeffs;
    test_x_coeffs << 10.0, 20.0, 30.0, 40.0;
    Eigen::Vector4d test_y_coeffs;
    test_y_coeffs << 50.0, 60.0, 70.0, 80.0;

    Coefficients2D combined = CombineCoeffs(test_x_coeffs, test_y_coeffs);
    EXPECT_NEAR(combined[0], 10.0, tolerance);
    EXPECT_NEAR(combined[1], 20.0, tolerance);
    EXPECT_NEAR(combined[2], 30.0, tolerance);
    EXPECT_NEAR(combined[3], 40.0, tolerance);
    EXPECT_NEAR(combined[4], 50.0, tolerance);
    EXPECT_NEAR(combined[5], 60.0, tolerance);
    EXPECT_NEAR(combined[6], 70.0, tolerance);
    EXPECT_NEAR(combined[7], 80.0, tolerance);
}

TEST_F(CubicPolynomial2DTest, LinearPolynomial2D) {
    // Test with linear polynomials (a=0, b=0): x(s) = 2s + 3, y(s) = -s + 1
    Coefficients2D linear_coeffs;
    linear_coeffs << 0.0, 0.0, 2.0, 3.0, 0.0, 0.0, -1.0, 1.0;

    Point2D result0 = Evaluate(linear_coeffs, 0.0);
    EXPECT_NEAR(result0.x(), 3.0, tolerance);
    EXPECT_NEAR(result0.y(), 1.0, tolerance);

    Point2D result1 = Evaluate(linear_coeffs, 1.0);
    EXPECT_NEAR(result1.x(), 5.0, tolerance);
    EXPECT_NEAR(result1.y(), 0.0, tolerance);

    Point2D result2 = Evaluate(linear_coeffs, 2.0);
    EXPECT_NEAR(result2.x(), 7.0, tolerance);
    EXPECT_NEAR(result2.y(), -1.0, tolerance);

    // Derivatives should be constant
    Heading2D deriv0 = EvaluateDerivative(linear_coeffs, 0.0);
    Heading2D deriv10 = EvaluateDerivative(linear_coeffs, 10.0);
    EXPECT_NEAR(deriv0.x(), 2.0, tolerance);
    EXPECT_NEAR(deriv0.y(), -1.0, tolerance);
    EXPECT_NEAR(deriv10.x(), 2.0, tolerance);
    EXPECT_NEAR(deriv10.y(), -1.0, tolerance);

    // Second derivatives should be zero
    Heading2D deriv2_0 = EvaluateSecondDerivative(linear_coeffs, 0.0);
    Heading2D deriv2_5 = EvaluateSecondDerivative(linear_coeffs, 5.0);
    EXPECT_NEAR(deriv2_0.x(), 0.0, tolerance);
    EXPECT_NEAR(deriv2_0.y(), 0.0, tolerance);
    EXPECT_NEAR(deriv2_5.x(), 0.0, tolerance);
    EXPECT_NEAR(deriv2_5.y(), 0.0, tolerance);
}

int main(int argc, char **argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
