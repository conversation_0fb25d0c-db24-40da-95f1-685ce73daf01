#include <gtest/gtest.h>
#include "cubic_spline_2d.hpp"
#include <cmath>

using namespace curves;

class CubicSpline2DTest : public ::testing::Test {
protected:
    void SetUp() override {
        tolerance = 1e-10;
        
        // Create a simple test polyline
        test_points = {
            Point2D(0.0, 0.0),
            Point2D(1.0, 1.0),
            Point2D(2.0, 0.0),
            Point2D(3.0, 1.0)
        };
    }
    
    double tolerance;
    std::vector<Point2D> test_points;
};

TEST_F(CubicSpline2DTest, ConstructorValidInput) {
    // Test constructor with valid input
    EXPECT_NO_THROW(Spline spline(test_points));

    Spline spline(test_points);
    EXPECT_EQ(spline.GetPointCount(), 4);
    EXPECT_EQ(spline.GetSegmentCount(), 3);

    const auto& polyline = spline.GetPolyline();
    EXPECT_EQ(polyline.size(), 4);
    for (size_t i = 0; i < test_points.size(); ++i) {
        EXPECT_NEAR(polyline[i].x(), test_points[i].x(), tolerance);
        EXPECT_NEAR(polyline[i].y(), test_points[i].y(), tolerance);
    }
}

TEST_F(CubicSpline2DTest, ConstructorInvalidInput) {
    // Test constructor with insufficient points
    std::vector<Point2D> single_point = {Point2D(0.0, 0.0)};
    EXPECT_THROW(Spline spline(single_point), std::invalid_argument);

    std::vector<Point2D> empty_points;
    EXPECT_THROW(Spline spline(empty_points), std::invalid_argument);
}

TEST_F(CubicSpline2DTest, AddPoint) {
    Spline spline(test_points);

    Point2D new_point(4.0, 2.0);
    spline.AddPoint(new_point);

    EXPECT_EQ(spline.GetPointCount(), 5);
    EXPECT_EQ(spline.GetSegmentCount(), 4);

    const auto& polyline = spline.GetPolyline();
    EXPECT_NEAR(polyline.back().x(), 4.0, tolerance);
    EXPECT_NEAR(polyline.back().y(), 2.0, tolerance);
}

TEST_F(CubicSpline2DTest, RemovePoint) {
    Spline spline(test_points);

    // Remove middle point
    spline.RemovePoint(1);

    EXPECT_EQ(spline.GetPointCount(), 3);
    EXPECT_EQ(spline.GetSegmentCount(), 2);

    const auto& polyline = spline.GetPolyline();
    EXPECT_NEAR(polyline[0].x(), 0.0, tolerance);
    EXPECT_NEAR(polyline[0].y(), 0.0, tolerance);
    EXPECT_NEAR(polyline[1].x(), 2.0, tolerance);
    EXPECT_NEAR(polyline[1].y(), 0.0, tolerance);
    EXPECT_NEAR(polyline[2].x(), 3.0, tolerance);
    EXPECT_NEAR(polyline[2].y(), 1.0, tolerance);
}

TEST_F(CubicSpline2DTest, RemovePointInvalidCases) {
    Spline spline(test_points);

    // Test out of range
    EXPECT_THROW(spline.RemovePoint(10), std::out_of_range);

    // Create spline with minimum points and try to remove
    std::vector<Point2D> min_points = {Point2D(0.0, 0.0), Point2D(1.0, 1.0)};
    Spline min_spline(min_points);
    EXPECT_THROW(min_spline.RemovePoint(0), std::invalid_argument);
}

TEST_F(CubicSpline2DTest, ModifyPoint) {
    Spline spline(test_points);

    Point2D new_point(1.5, 0.5);
    spline.ModifyPoint(1, new_point);

    const auto& polyline = spline.GetPolyline();
    EXPECT_NEAR(polyline[1].x(), 1.5, tolerance);
    EXPECT_NEAR(polyline[1].y(), 0.5, tolerance);

    // Test out of range
    EXPECT_THROW(spline.ModifyPoint(10, new_point), std::out_of_range);
}

TEST_F(CubicSpline2DTest, EvaluateAtControlPoints) {
    Spline spline(test_points);

    // Test that spline passes through all control points
    for (size_t i = 0; i < test_points.size(); ++i) {
        Point2D evaluated = spline.Evaluate(static_cast<double>(i));
        EXPECT_NEAR(evaluated.x(), test_points[i].x(), tolerance);
        EXPECT_NEAR(evaluated.y(), test_points[i].y(), tolerance);
    }
}

TEST_F(CubicSpline2DTest, EvaluateOutOfRange) {
    Spline spline(test_points);

    // Test out of range values
    EXPECT_THROW(spline.Evaluate(-0.1), std::out_of_range);
    EXPECT_THROW(spline.Evaluate(3.1), std::out_of_range);
}

TEST_F(CubicSpline2DTest, EvaluateIntermediate) {
    Spline spline(test_points);

    // Test evaluation at intermediate points
    Point2D mid_point = spline.Evaluate(0.5);
    // Should be somewhere between first and second control points
    EXPECT_GT(mid_point.x(), 0.0);
    EXPECT_LT(mid_point.x(), 1.0);
    EXPECT_GT(mid_point.y(), 0.0);
    EXPECT_LT(mid_point.y(), 1.0);

    // Test evaluation at segment boundaries
    Point2D boundary_point = spline.Evaluate(1.0);
    EXPECT_NEAR(boundary_point.x(), test_points[1].x(), tolerance);
    EXPECT_NEAR(boundary_point.y(), test_points[1].y(), tolerance);
}

TEST_F(CubicSpline2DTest, EvaluateDerivative) {
    Spline spline(test_points);

    // Test derivative evaluation
    Heading2D derivative = spline.EvaluateDerivative(0.0);
    // Derivative should be non-zero
    EXPECT_GT(derivative.norm(), tolerance);

    // Test derivative at different points
    for (double s = 0.0; s <= 3.0; s += 0.5) {
        EXPECT_NO_THROW(spline.EvaluateDerivative(s));
    }
}

TEST_F(CubicSpline2DTest, EvaluateSecondDerivative) {
    Spline spline(test_points);

    // Test second derivative evaluation
    for (double s = 0.0; s <= 3.0; s += 0.5) {
        EXPECT_NO_THROW(spline.EvaluateSecondDerivative(s));
    }
}

TEST_F(CubicSpline2DTest, EvaluateMultiple) {
    Spline spline(test_points);

    std::vector<double> s_values = {0.0, 0.5, 1.0, 1.5, 2.0, 2.5, 3.0};
    std::vector<Point2D> results = spline.EvaluateMultiple(s_values);

    EXPECT_EQ(results.size(), s_values.size());

    // Verify results match individual evaluations
    for (size_t i = 0; i < s_values.size(); ++i) {
        Point2D individual = spline.Evaluate(s_values[i]);
        EXPECT_NEAR(results[i].x(), individual.x(), tolerance);
        EXPECT_NEAR(results[i].y(), individual.y(), tolerance);
    }
}

TEST_F(CubicSpline2DTest, StraightLineSpline) {
    // Test with points on a straight line
    std::vector<Point2D> line_points = {
        Point2D(0.0, 0.0),
        Point2D(1.0, 1.0),
        Point2D(2.0, 2.0)
    };

    Spline spline(line_points);

    // Test that intermediate points lie approximately on the line
    for (double s = 0.0; s <= 2.0; s += 0.1) {
        Point2D point = spline.Evaluate(s);
        // For a straight line y = x, the difference should be small
        EXPECT_NEAR(point.y(), point.x(), 0.1); // Allow some tolerance for cubic interpolation
    }
}

TEST_F(CubicSpline2DTest, ContinuityAtSegmentBoundaries) {
    Spline spline(test_points);

    // Test continuity at segment boundaries
    for (size_t i = 1; i < test_points.size() - 1; ++i) {
        double s = static_cast<double>(i);

        // Evaluate just before and after the boundary
        Point2D before = spline.Evaluate(s - 1e-8);
        Point2D after = spline.Evaluate(s + 1e-8);
        Point2D at_boundary = spline.Evaluate(s);

        // Check position continuity
        EXPECT_NEAR(before.x(), at_boundary.x(), 1e-6);
        EXPECT_NEAR(before.y(), at_boundary.y(), 1e-6);
        EXPECT_NEAR(after.x(), at_boundary.x(), 1e-6);
        EXPECT_NEAR(after.y(), at_boundary.y(), 1e-6);
    }
}

TEST_F(CubicSpline2DTest, RegenerationAfterModification) {
    Spline spline(test_points);

    // Evaluate before modification
    Point2D before = spline.Evaluate(0.5);

    // Modify a point
    spline.ModifyPoint(1, Point2D(1.0, 2.0));

    // Evaluate after modification
    Point2D after = spline.Evaluate(0.5);

    // Results should be different
    EXPECT_FALSE(std::abs(before.x() - after.x()) < tolerance &&
                 std::abs(before.y() - after.y()) < tolerance);
}
