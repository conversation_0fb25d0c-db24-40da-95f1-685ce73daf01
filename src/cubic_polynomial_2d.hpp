#pragma once

#include <Eigen/Dense>
#include <vector>
#include <stdexcept>

namespace curves {
    /**
     * Represents a 2D cubic polynomial P(s) = (x(s), y(s))
     * where x(s) = a₁s³ + b₁s² + c₁s + d₁ and y(s) = a₂s³ + b₂s² + c₂s + d₂
     * Coefficients are stored as [a₁, b₁, c₁, d₁, a₂, b₂, c₂, d₂]
     */
    using Coefficients2D = Eigen::Matrix<double, 8, 1>;

    /**
     * Represents a 2D point
     */
    using Point2D = Eigen::Vector2d;

    /**
     * Represents a heading (direction vector)
     */
    using Heading2D = Eigen::Vector2d;

    /**
     * Evaluate 2D cubic polynomial at given s
     * @param coeffs Polynomial coefficients [a₁, b₁, c₁, d₁, a₂, b₂, c₂, d₂]
     * @param s Parameter value to evaluate at
     * @return P(s) = (x(s), y(s))
     */
    Point2D Evaluate(const Coefficients2D &coeffs, double s);

    /**
     * Evaluate first derivative of 2D cubic polynomial at given s
     * @param coeffs Polynomial coefficients [a₁, b₁, c₁, d₁, a₂, b₂, c₂, d₂]
     * @param s Parameter value to evaluate at
     * @return P'(s) = (x'(s), y'(s)) - tangent vector
     */
    Heading2D EvaluateDerivative(const Coefficients2D &coeffs, double s);

    /**
     * Evaluate second derivative of 2D cubic polynomial at given s
     * @param coeffs Polynomial coefficients [a₁, b₁, c₁, d₁, a₂, b₂, c₂, d₂]
     * @param s Parameter value to evaluate at
     * @return P''(s) = (x''(s), y''(s)) - curvature vector
     */
    Heading2D EvaluateSecondDerivative(const Coefficients2D &coeffs, double s);

    /**
     * Fit 2D cubic polynomial through 4 points
     * @param points Vector of 4 2D points
     * @param s_values Vector of 4 parameter values [s₀, s₁, s₂, s₃]
     * @return Polynomial coefficients [a₁, b₁, c₁, d₁, a₂, b₂, c₂, d₂]
     * @throws std::invalid_argument if not exactly 4 points provided
     */
    Coefficients2D FitFourPoints2D(const std::vector<Point2D> &points, std::vector<double> &s_values);

    /**
     * Fit 2D cubic polynomial given two points and their headings
     * This creates a Hermite interpolation where:
     * - P(s₀) = point₀, P'(s₀) = heading₀
     * - P(s₁) = point₁, P'(s₁) = heading₁
     * @param point0 First point (x₀, y₀)
     * @param heading0 Heading at first point (direction vector)
     * @param point1 Second point (x₁, y₁)
     * @param heading1 Heading at second point (direction vector)
     * @param s0 Parameter value at first point
     * @param s1 Parameter value at second point
     * @return Polynomial coefficients [a₁, b₁, c₁, d₁, a₂, b₂, c₂, d₂]
     */
    Coefficients2D FitPointHeadingPointHeading2D(
        const Point2D &point0, const Heading2D &heading0,
        const Point2D &point1, const Heading2D &heading1,
        const double &s0 = 0.0, const double &s1 = 1.0
    );

    /**
     * Get polynomial coefficients as a string representation
     * @param coeffs Polynomial coefficients [a₁, b₁, c₁, d₁, a₂, b₂, c₂, d₂]
     * @return String representation of both x(s) and y(s) polynomials
     */
    std::string ToString(const Coefficients2D &coeffs);

    /**
     * Evaluate 2D polynomial at multiple s values
     * @param coeffs Polynomial coefficients [a₁, b₁, c₁, d₁, a₂, b₂, c₂, d₂]
     * @param s_values Vector of s values to evaluate at
     * @return Vector of corresponding 2D points
     */
    std::vector<Point2D> EvaluateMultiple(const Coefficients2D &coeffs, const std::vector<double> &s_values);

    /**
     * Extract x-component coefficients from 2D coefficients
     * @param coeffs2d 2D polynomial coefficients [a₁, b₁, c₁, d₁, a₂, b₂, c₂, d₂]
     * @return x-component coefficients [a₁, b₁, c₁, d₁]
     */
    Eigen::Vector4d GetXCoeffs(const Coefficients2D &coeffs2d);

    /**
     * Extract y-component coefficients from 2D coefficients
     * @param coeffs2d 2D polynomial coefficients [a₁, b₁, c₁, d₁, a₂, b₂, c₂, d₂]
     * @return y-component coefficients [a₂, b₂, c₂, d₂]
     */
    Eigen::Vector4d GetYCoeffs(const Coefficients2D &coeffs2d);

    /**
     * Create 2D coefficients from separate x and y coefficients
     * @param x_coeffs x-component coefficients [a₁, b₁, c₁, d₁]
     * @param y_coeffs y-component coefficients [a₂, b₂, c₂, d₂]
     * @return Combined 2D coefficients [a₁, b₁, c₁, d₁, a₂, b₂, c₂, d₂]
     */
    Coefficients2D CombineCoeffs(const Eigen::Vector4d &x_coeffs, const Eigen::Vector4d &y_coeffs);

} // namespace curves
