#include <iostream>
#include <iomanip>
#include "cubic_polynomial.hpp"
#include "cubic_polynomial_2d.hpp"
#include "cubic_spline_2d.hpp"

using namespace curves;

void demonstrate1DCubicPolynomials() {
    std::cout << "========================================" << std::endl;
    std::cout << "=== 1D CUBIC POLYNOMIAL EXAMPLES ===" << std::endl;
    std::cout << "========================================" << std::endl;

    // Demo 1: Basic Polynomial Evaluation
    std::cout << "\n=== Basic Polynomial Evaluation ===" << std::endl;

    // Create polynomial f(s) = 2s³ - 3s² + s + 5
    Coefficients coeffs;
    coeffs << 2.0, -3.0, 1.0, 5.0;

    std::cout << "Polynomial: " << ToString(coeffs) << std::endl;

    // Evaluate at several points
    std::vector<double> s_values = {-2.0, -1.0, 0.0, 1.0, 2.0, 3.0};

    std::cout << std::fixed << std::setprecision(3);
    std::cout << "\nEvaluation table:" << std::endl;
    std::cout << "s\tf(s)\tf'(s)\tf''(s)" << std::endl;
    std::cout << "---\t----\t-----\t------" << std::endl;

    for (double s: s_values) {
        double f = Evaluate(coeffs, s);
        double fp = EvaluateDerivative(coeffs, s);
        double fpp = EvaluateSecondDerivative(coeffs, s);
        std::cout << s << "\t" << f << "\t" << fp << "\t" << fpp << std::endl;
    }

    // Demo 2: Four Point Fitting
    std::cout << "\n\n=== Four Point Fitting ===" << std::endl;

    // Create 4 points for fitting
    std::vector<double> points = {0.0, 1.0, 2.0, 3.0};

    std::cout << "Input points:" << std::endl;
    for (size_t i = 0; i < points.size(); ++i) {
        std::cout << "  Point " << i + 1 << ": (" << points[i] << ")" << std::endl;
    }

    auto s_vals = std::vector<double>({});
    Coefficients fitted = FitFourPoints1D(points, s_vals);
    std::cout << "\nFitted polynomial coefficients: " << ToString(fitted) << std::endl;

    // Verify the fit
    std::cout << "\nVerification (should match input points):" << std::endl;
    for (size_t i = 0; i < points.size(); i++) {
        double s = s_vals[i];
        double x_expected = points[i];
        double x_fitted = Evaluate(fitted, s);
        std::cout << "  s=" << s << ": expected=" << x_expected << ", fitted=" << x_fitted
            << ", error=" << std::abs(x_expected - x_fitted) << std::endl;
    }
}

void demonstrate2DCubicPolynomials() {
    std::cout << "\n\n========================================" << std::endl;
    std::cout << "=== 2D CUBIC POLYNOMIAL EXAMPLES ===" << std::endl;
    std::cout << "========================================" << std::endl;

    std::cout << std::fixed << std::setprecision(3);

    // Demo 1: Point-Heading-Point-Heading fitting
    std::cout << "\n=== Point-Heading-Point-Heading Fitting ===" << std::endl;
    
    Point2D start_point(0.0, 0.0);
    Heading2D start_heading(1.0, 0.0);  // Moving right
    Point2D end_point(3.0, 2.0);
    Heading2D end_heading(0.0, 1.0);    // Moving up
    
    std::cout << "Start: (" << start_point.x() << ", " << start_point.y() 
              << ") with heading (" << start_heading.x() << ", " << start_heading.y() << ")" << std::endl;
    std::cout << "End: (" << end_point.x() << ", " << end_point.y() 
              << ") with heading (" << end_heading.x() << ", " << end_heading.y() << ")" << std::endl;

    Coefficients2D coeffs = FitPointHeadingPointHeading2D(start_point, start_heading, end_point, end_heading);
    
    std::cout << "\nFitted polynomial:" << std::endl;
    std::cout << ToString(coeffs) << std::endl;

    // Evaluate at several points
    std::cout << "\nEvaluation along the curve:" << std::endl;
    std::cout << "s\tx(s)\ty(s)\tdx/ds\tdy/ds" << std::endl;
    for (double s = 0.0; s <= 1.0; s += 0.25) {
        Point2D point = Evaluate(coeffs, s);
        Heading2D tangent = EvaluateDerivative(coeffs, s);
        std::cout << s << "\t" << point.x() << "\t" << point.y() 
                  << "\t" << tangent.x() << "\t" << tangent.y() << std::endl;
    }

    // Demo 2: Four Points fitting
    std::cout << "\n\n=== Four Points Fitting ===" << std::endl;

    std::vector<Point2D> points = {
        Point2D(0.0, 0.0),
        Point2D(1.0, 1.0),
        Point2D(2.0, 1.5),
        Point2D(3.0, 0.0)
    };
    std::vector<double> s_values = {0.0, 0.33, 0.67, 1.0};
    
    std::cout << "Fitting through points:" << std::endl;
    for (size_t i = 0; i < points.size(); ++i) {
        std::cout << "s=" << s_values[i] << ": (" << points[i].x() << ", " << points[i].y() << ")" << std::endl;
    }

    Coefficients2D coeffs2 = FitFourPoints2D(points, s_values);
    
    std::cout << "\nFitted polynomial:" << std::endl;
    std::cout << ToString(coeffs2) << std::endl;

    // Verify the fit
    std::cout << "\nVerification (should match input points):" << std::endl;
    std::cout << "s\tExpected\t\tActual" << std::endl;
    for (size_t i = 0; i < points.size(); ++i) {
        Point2D actual = Evaluate(coeffs2, s_values[i]);
        std::cout << s_values[i] << "\t(" << points[i].x() << ", " << points[i].y() 
                  << ")\t\t(" << actual.x() << ", " << actual.y() << ")" << std::endl;
    }

    // Demo 3: Curvature analysis
    std::cout << "\n\n=== Curvature Analysis ===" << std::endl;
    std::cout << "s\tCurvature Vector (d²x/ds², d²y/ds²)" << std::endl;
    for (double s = 0.0; s <= 1.0; s += 0.25) {
        Heading2D curvature = EvaluateSecondDerivative(coeffs, s);
        std::cout << s << "\t(" << curvature.x() << ", " << curvature.y() << ")" << std::endl;
    }
}

void demonstrate2DCubicSplines() {
    std::cout << "\n\n========================================" << std::endl;
    std::cout << "=== 2D CUBIC SPLINE EXAMPLES ===" << std::endl;
    std::cout << "========================================" << std::endl;
    
    // Create initial control points for a curved path
    std::vector<Point2D> control_points = {
        Point2D(0.0, 0.0),    // Start point
        Point2D(1.0, 2.0),    // First control point
        Point2D(3.0, 1.0),    // Second control point
        Point2D(4.0, 3.0),    // Third control point
        Point2D(5.0, 2.0)     // End point
    };
    
    std::cout << "\nInitial control points:" << std::endl;
    for (size_t i = 0; i < control_points.size(); ++i) {
        std::cout << "  Point " << i << ": (" << control_points[i].x() 
                  << ", " << control_points[i].y() << ")" << std::endl;
    }
    
    // Create the spline
    Spline spline(control_points);
    
    std::cout << "\nSpline created with " << spline.GetPointCount() 
              << " control points and " << spline.GetSegmentCount() 
              << " polynomial segments." << std::endl;
    
    // Evaluate the spline at various points
    std::cout << "\n=== Spline Evaluation ===" << std::endl;
    std::cout << std::fixed << std::setprecision(3);
    std::cout << "s\t\tx(s)\t\ty(s)\t\tdx/ds\t\tdy/ds\t\td²x/ds²\t\td²y/ds²" << std::endl;
    std::cout << "---\t\t---\t\t---\t\t---\t\t---\t\t---\t\t---" << std::endl;
    
    for (double s = 0.0; s <= 4.0; s += 0.5) {
        Point2D position = spline.Evaluate(s);
        Heading2D velocity = spline.EvaluateDerivative(s);
        Heading2D acceleration = spline.EvaluateSecondDerivative(s);
        
        std::cout << s << "\t\t" 
                  << position.x() << "\t\t" << position.y() << "\t\t"
                  << velocity.x() << "\t\t" << velocity.y() << "\t\t"
                  << acceleration.x() << "\t\t" << acceleration.y() << std::endl;
    }
    
    // Demonstrate adding a point
    std::cout << "\n=== Adding a Point ===" << std::endl;
    Point2D new_point(6.0, 1.0);
    spline.AddPoint(new_point);
    std::cout << "Added point: (" << new_point.x() << ", " << new_point.y() << ")" << std::endl;
    std::cout << "Spline now has " << spline.GetPointCount() << " control points." << std::endl;
    
    // Evaluate at the new end point
    Point2D end_position = spline.Evaluate(5.0);
    std::cout << "Position at s=5.0: (" << end_position.x() << ", " << end_position.y() << ")" << std::endl;
    
    // Demonstrate modifying a point
    std::cout << "\n=== Modifying a Point ===" << std::endl;
    Point2D old_point = spline.GetPolyline()[2];
    Point2D modified_point(3.0, 0.5);
    std::cout << "Changing point 2 from (" << old_point.x() << ", " << old_point.y() 
              << ") to (" << modified_point.x() << ", " << modified_point.y() << ")" << std::endl;
    
    spline.ModifyPoint(2, modified_point);
    
    // Show how this affects the spline at s=2.0
    Point2D position_at_2 = spline.Evaluate(2.0);
    std::cout << "Position at s=2.0 after modification: (" 
              << position_at_2.x() << ", " << position_at_2.y() << ")" << std::endl;
    
    // Demonstrate removing a point
    std::cout << "\n=== Removing a Point ===" << std::endl;
    std::cout << "Removing point at index 1..." << std::endl;
    spline.RemovePoint(1);
    std::cout << "Spline now has " << spline.GetPointCount() << " control points." << std::endl;
    
    // Show the updated control points
    std::cout << "\nUpdated control points:" << std::endl;
    const auto& polyline = spline.GetPolyline();
    for (size_t i = 0; i < polyline.size(); ++i) {
        std::cout << "  Point " << i << ": (" << polyline[i].x() 
                  << ", " << polyline[i].y() << ")" << std::endl;
    }
    
    // Demonstrate batch evaluation
    std::cout << "\n=== Batch Evaluation ===" << std::endl;
    std::vector<double> s_values = {0.0, 0.75, 1.5, 2.25, 3.0};
    std::vector<Point2D> batch_results = spline.EvaluateMultiple(s_values);
    
    std::cout << "Batch evaluation results:" << std::endl;
    for (size_t i = 0; i < s_values.size(); ++i) {
        std::cout << "  s=" << s_values[i] << " -> (" 
                  << batch_results[i].x() << ", " << batch_results[i].y() << ")" << std::endl;
    }
    
    // Demonstrate continuity at control points
    std::cout << "\n=== Continuity Verification ===" << std::endl;
    std::cout << "Verifying that spline passes through all control points:" << std::endl;
    for (size_t i = 0; i < polyline.size(); ++i) {
        Point2D evaluated = spline.Evaluate(static_cast<double>(i));
        double error_x = std::abs(evaluated.x() - polyline[i].x());
        double error_y = std::abs(evaluated.y() - polyline[i].y());
        std::cout << "  Point " << i << ": error = (" << error_x << ", " << error_y << ")" << std::endl;
    }
}

int main() {
    std::cout << "CURVES LIBRARY DEMONSTRATION" << std::endl;
    std::cout << "=============================" << std::endl;
    std::cout << "This demo showcases all curve types in the unified 'curves' namespace." << std::endl;

    try {
        demonstrate1DCubicPolynomials();
        demonstrate2DCubicPolynomials();
        demonstrate2DCubicSplines();

        std::cout << "\n\n========================================" << std::endl;
        std::cout << "=== ALL DEMONSTRATIONS COMPLETED ===" << std::endl;
        std::cout << "========================================" << std::endl;
        std::cout << "All demonstrations completed successfully!" << std::endl;
    } catch (const std::exception &e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
