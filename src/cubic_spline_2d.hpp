#pragma once

#include "cubic_polynomial_2d.hpp"
#include <vector>
#include <stdexcept>

namespace curves {
    
    /**
     * A 2D cubic spline class that maintains a polyline of 2D points and
     * fits cubic polynomials between consecutive points using point-heading-point-heading fitting.
     *
     * The spline automatically regenerates cubic polynomial segments whenever points are
     * added, removed, or modified. Each segment uses Hermite interpolation with headings
     * computed from neighboring points.
     */
    class Spline {
    private:
        std::vector<Point2D> polyline_;
        std::vector<Coefficients2D> segments_;
        bool needs_regeneration_;
        
        /**
         * Regenerate all cubic polynomial segments based on current polyline
         * Uses point-heading-point-heading fitting between consecutive points
         */
        void RegeneratePolynomials();
        
        /**
         * Compute heading (direction vector) for a point in the polyline
         * @param index Index of the point in the polyline
         * @return Normalized heading vector
         */
        Heading2D ComputeHeading(size_t index) const;
        
        /**
         * Ensure polynomials are up to date, regenerating if needed
         */
        void EnsurePolynomialsUpdated();
        
        /**
         * Find which segment contains the given s value and compute local s
         * @param s Global parameter value
         * @param segment_index Output: index of the segment
         * @param local_s Output: local s value within the segment [0,1]
         */
        void FindSegment(double s, size_t& segment_index, double& local_s) const;
        
    public:
        /**
         * Constructor with initial polyline points
         * @param points Initial list of 2D points (must have at least 2 points)
         * @throws std::invalid_argument if less than 2 points provided
         */
        explicit Spline(const std::vector<Point2D>& points);
        
        /**
         * Add a point to the end of the polyline
         * @param point 2D point to add
         */
        void AddPoint(const Point2D& point);
        
        /**
         * Remove a point from the polyline
         * @param index Index of the point to remove
         * @throws std::out_of_range if index is invalid
         * @throws std::invalid_argument if removing would leave less than 2 points
         */
        void RemovePoint(size_t index);
        
        /**
         * Modify an existing point in the polyline
         * @param index Index of the point to modify
         * @param new_point New position for the point
         * @throws std::out_of_range if index is invalid
         */
        void ModifyPoint(size_t index, const Point2D& new_point);
        
        /**
         * Get the current polyline points
         * @return Vector of 2D points
         */
        const std::vector<Point2D>& GetPolyline() const;
        
        /**
         * Get the number of points in the polyline
         * @return Number of points
         */
        size_t GetPointCount() const;
        
        /**
         * Get the number of polynomial segments
         * @return Number of segments (always getPointCount() - 1)
         */
        size_t GetSegmentCount() const;
        
        /**
         * Evaluate the spline at parameter s
         * @param s Parameter value (s=0 at first point, s=1 at second point, etc.)
         * @return 2D point on the spline
         * @throws std::out_of_range if s is outside valid range [0, getPointCount()-1]
         */
        Point2D Evaluate(double s);

        /**
         * Evaluate the first derivative of the spline at parameter s
         * @param s Parameter value
         * @return 2D tangent vector
         * @throws std::out_of_range if s is outside valid range [0, getPointCount()-1]
         */
        Heading2D EvaluateDerivative(double s);

        /**
         * Evaluate the second derivative of the spline at parameter s
         * @param s Parameter value
         * @return 2D curvature vector
         * @throws std::out_of_range if s is outside valid range [0, getPointCount()-1]
         */
        Heading2D EvaluateSecondDerivative(double s);

        /**
         * Evaluate the spline at multiple parameter values
         * @param s_values Vector of parameter values
         * @return Vector of 2D points on the spline
         */
        std::vector<Point2D> EvaluateMultiple(const std::vector<double>& s_values);
    };
    
} // namespace curves
