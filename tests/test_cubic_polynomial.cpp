#include <gtest/gtest.h>
#include "cubic_polynomial.hpp"
#include <cmath>

using namespace curves;

class CubicPolynomialTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Test polynomial: f(s) = 2s³ - 3s² + s + 5
        test_coeffs << 2.0, -3.0, 1.0, 5.0;
    }

    Coefficients test_coeffs;
    const double tolerance = 1e-10;
};

TEST_F(CubicPolynomialTest, EvaluateBasic) {
    // f(s) = 2s³ - 3s² + s + 5
    // f(0) = 5
    EXPECT_NEAR(Evaluate(test_coeffs, 0.0), 5.0, tolerance);

    // f(1) = 2 - 3 + 1 + 5 = 5
    EXPECT_NEAR(Evaluate(test_coeffs, 1.0), 5.0, tolerance);

    // f(2) = 16 - 12 + 2 + 5 = 11
    EXPECT_NEAR(Evaluate(test_coeffs, 2.0), 11.0, tolerance);

    // f(-1) = -2 - 3 - 1 + 5 = -1
    EXPECT_NEAR(Evaluate(test_coeffs, -1.0), -1.0, tolerance);
}

TEST_F(CubicPolynomialTest, EvaluateDerivative) {
    // f'(s) = 6s² - 6s + 1
    // f'(0) = 1
    EXPECT_NEAR(EvaluateDerivative(test_coeffs, 0.0), 1.0, tolerance);

    // f'(1) = 6 - 6 + 1 = 1
    EXPECT_NEAR(EvaluateDerivative(test_coeffs, 1.0), 1.0, tolerance);

    // f'(2) = 24 - 12 + 1 = 13
    EXPECT_NEAR(EvaluateDerivative(test_coeffs, 2.0), 13.0, tolerance);
}

TEST_F(CubicPolynomialTest, EvaluateSecondDerivative) {
    // f''(s) = 12s - 6
    // f''(0) = -6
    EXPECT_NEAR(EvaluateSecondDerivative(test_coeffs, 0.0), -6.0, tolerance);

    // f''(1) = 12 - 6 = 6
    EXPECT_NEAR(EvaluateSecondDerivative(test_coeffs, 1.0), 6.0, tolerance);

    // f''(0.5) = 6 - 6 = 0
    EXPECT_NEAR(EvaluateSecondDerivative(test_coeffs, 0.5), 0.0, tolerance);
}

TEST_F(CubicPolynomialTest, FitFourPoints) {
    // Create 4 points from known polynomial f(s) = s³ - 2s² + 3s - 1
    std::vector<double> s_values = {0.0, 0.5, 0.75, 1.0};
    std::vector<double> points = {-1.0, 0.125, 0.546875, 1.0};

    Coefficients fitted = FitFourPoints1D(points, s_values);

    // Should recover original coefficients [1, -2, 3, -1]
    EXPECT_NEAR(fitted[0], 1.0, tolerance); // a
    EXPECT_NEAR(fitted[1], -2.0, tolerance); // b
    EXPECT_NEAR(fitted[2], 3.0, tolerance); // c
    EXPECT_NEAR(fitted[3], -1.0, tolerance); // d
}

TEST_F(CubicPolynomialTest, FitFourPointsInvalidInput) {
    std::vector<double> points = {
        0.0, 1.0, 2.0, // Only 3 points
    };
    std::vector<double> s_values = {};

    EXPECT_THROW(FitFourPoints1D(points, s_values), std::invalid_argument);
}

TEST_F(CubicPolynomialTest, FitPointHeadingPointHeading) {
    // Test point-slop-point-slope
    double x0 = 0.0;
    double m0 = 0.0;
    double x1 = 1.0;
    double m1 = 1.0;

    Coefficients fitted = FitPointSlopePointSlope1D(x0, m0, x1, m1);

    // Verify the constraints are satisfied
    EXPECT_NEAR(Evaluate(fitted, 0.0), 0.0, tolerance); // f(0) = 0
    EXPECT_NEAR(Evaluate(fitted, 1.0), 1.0, tolerance); // f(1) = 1
    EXPECT_NEAR(EvaluateDerivative(fitted, 0.0), 0.0, tolerance); // f'(0) = 0
    EXPECT_NEAR(EvaluateDerivative(fitted, 1.0), 1.0, tolerance); // f'(1) = 1
}

TEST_F(CubicPolynomialTest, ToString) {
    Coefficients coeffs;
    coeffs << 1.0, -2.0, 3.0, -4.0; // s³ - 2s² + 3s - 4

    std::string result = ToString(coeffs);

    // Should contain the main terms (exact format may vary)
    EXPECT_TRUE(result.find("s³") != std::string::npos);
    EXPECT_TRUE(result.find("s²") != std::string::npos);
    EXPECT_TRUE(result.find("s") != std::string::npos);
}

TEST_F(CubicPolynomialTest, EvaluateMultiple) {
    std::vector<double> s_values = {0.0, 1.0, 2.0};
    std::vector<double> results = EvaluateMultiple(test_coeffs, s_values);

    ASSERT_EQ(results.size(), 3);
    EXPECT_NEAR(results[0], Evaluate(test_coeffs, 0.0), tolerance);
    EXPECT_NEAR(results[1], Evaluate(test_coeffs, 1.0), tolerance);
    EXPECT_NEAR(results[2], Evaluate(test_coeffs, 2.0), tolerance);
}

TEST_F(CubicPolynomialTest, LinearPolynomial) {
    // Test with a linear polynomial (a=0, b=0): f(s) = 2s + 3
    Coefficients linear_coeffs;
    linear_coeffs << 0.0, 0.0, 2.0, 3.0;

    EXPECT_NEAR(Evaluate(linear_coeffs, 0.0), 3.0, tolerance);
    EXPECT_NEAR(Evaluate(linear_coeffs, 1.0), 5.0, tolerance);
    EXPECT_NEAR(Evaluate(linear_coeffs, 2.0), 7.0, tolerance);

    // Derivative should be constant
    EXPECT_NEAR(EvaluateDerivative(linear_coeffs, 0.0), 2.0, tolerance);
    EXPECT_NEAR(EvaluateDerivative(linear_coeffs, 10.0), 2.0, tolerance);

    // Second derivative should be zero
    EXPECT_NEAR(EvaluateSecondDerivative(linear_coeffs, 0.0), 0.0, tolerance);
    EXPECT_NEAR(EvaluateSecondDerivative(linear_coeffs, 5.0), 0.0, tolerance);
}

int main(int argc, char **argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
