cmake_minimum_required(VERSION 3.16)
project(CubicPolynomialFitting)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(Eigen3 REQUIRED)
find_package(GTest REQUIRED)

# Include directories
include_directories(src)

# Create the curves library
add_library(curves STATIC
    src/cubic_polynomial.cpp
    src/cubic_polynomial_2d.cpp
    src/cubic_spline_2d.cpp
)

# Link Eigen to the library
target_link_libraries(curves Eigen3::Eigen)

# Create the combined demo executable
add_executable(curves_demo src/demo.cpp)
target_link_libraries(curves_demo curves)

# Create the test executable
add_executable(test_cubic_polynomial tests/test_cubic_polynomial.cpp)
target_link_libraries(test_cubic_polynomial
    cubic_polynomial
    GTest::gtest
    GTest::gtest_main
)

# Create the 2D test executable
add_executable(test_cubic_polynomial_2d tests/test_cubic_polynomial_2d.cpp)
target_link_libraries(test_cubic_polynomial_2d
    cubic_polynomial
    GTest::gtest
    GTest::gtest_main
)

# Create the cubic spline 2D test executable
add_executable(test_cubic_spline_2d tests/test_cubic_spline_2d.cpp)
target_link_libraries(test_cubic_spline_2d
    cubic_polynomial
    GTest::gtest
    GTest::gtest_main
)

# Enable testing
enable_testing()
add_test(NAME CubicPolynomialTests COMMAND test_cubic_polynomial)
add_test(NAME CubicPolynomial2DTests COMMAND test_cubic_polynomial_2d)
add_test(NAME CubicSpline2DTests COMMAND test_cubic_spline_2d)
